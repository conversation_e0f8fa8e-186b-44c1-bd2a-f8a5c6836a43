// Simple test to check if galaxy permission functionality works
const axios = require('axios');

async function testGalaxyPermission() {
    try {
        console.log('Testing galaxy permission functionality...');
        
        // Test creating a workflow with galaxy project ID
        const workflowData = {
            name: 'Test Galaxy Workflow',
            nodes: [
                {
                    id: 'start',
                    type: 'n8n-nodes-base.start',
                    typeVersion: 1,
                    position: [100, 100],
                    parameters: {}
                }
            ],
            connections: {},
            galaxyProjectId: 'test-galaxy-project-123'
        };
        
        console.log('Sending request to create workflow with galaxy project ID...');
        
        // This would normally be sent to the n8n API
        // For now, we'll just log what would be sent
        console.log('Workflow data that would be sent:', JSON.stringify(workflowData, null, 2));
        
        console.log('Test completed - check the n8n logs for "Add Galaxy Permit Record" message');
        
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

testGalaxyPermission();
