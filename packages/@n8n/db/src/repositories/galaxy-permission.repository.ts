import { Service } from '@n8n/di';
import { Repository } from '@n8n/typeorm';

import { TiDbDataSourceService } from '../connection/tidb-datasource.service';
import { GalaxyPermission } from '../entities';

@Service()
export class GalaxyPermissionRepository extends Repository<GalaxyPermission> {
	constructor(tiDbDataSourceService: TiDbDataSourceService) {
		super(GalaxyPermission, tiDbDataSourceService.dataSource.manager);
	}

	async addGalaxyPermitRecord(gPermit: Omit<GalaxyPermission, 'id'>) {
		try {
			console.log('Saving galaxy permit record:', gPermit);
			const result = await this.save(gPermit);
			console.log('Galaxy permit record saved successfully:', result);
			return result;
		} catch (error) {
			console.error('Failed to save galaxy permit record:', error);
			throw error;
		}
	}

	async deleteGalaxyPermitRecord(workflowID: string) {
		return await this.delete({ res_id: workflowID });
	}
}
