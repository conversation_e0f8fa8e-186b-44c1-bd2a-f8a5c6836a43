import { GlobalConfig } from '@n8n/config';
import { GalaxyPermission, GalaxyPermissionRepository } from '@n8n/db';
import { Service } from '@n8n/di';

@Service()
export class GalaxyService {
	constructor(private readonly galaxyPermissionRepository: GalaxyPermissionRepository) {}

	async addGalaxyPermitRecord(gPermit: Omit<GalaxyPermission, 'id'>) {
		return await this.galaxyPermissionRepository.addGalaxyPermitRecord(gPermit);
	}

	async deleteGalaxyPermitRecord(workflowID: string) {
		return await this.galaxyPermissionRepository.deleteGalaxyPermitRecord(workflowID);
	}

	async getRefProject(uid: string) {
		const prjmUrl = GlobalConfig;
		return await this.galaxyPermissionRepository.find({ where: { res_id: uid } });
	}
}
