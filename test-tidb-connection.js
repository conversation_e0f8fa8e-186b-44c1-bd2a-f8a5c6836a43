const { Container } = require('@n8n/di');
const { TiDbConnection, GalaxyPermissionRepository } = require('@n8n/db');

async function testTiDbConnection() {
    try {
        console.log('Testing TiDB connection...');
        
        // Get TiDB connection from container
        const tiDbConnection = Container.get(TiDbConnection);
        
        // Initialize connection
        await tiDbConnection.init();
        console.log('TiDB connection initialized successfully');
        
        // Test if galaxy_permission table exists by trying to query it
        const galaxyRepo = Container.get(GalaxyPermissionRepository);
        
        // Try to find all records (this will fail if table doesn't exist)
        const records = await galaxyRepo.find();
        console.log('Galaxy permission table exists and is accessible');
        console.log('Current records:', records.length);
        
        // Test adding a record
        const testRecord = {
            res_id: 'test-workflow-id',
            res_type: 'workflow',
            project_id: 'test-project-id'
        };
        
        const savedRecord = await galaxyRepo.addGalaxyPermitRecord(testRecord);
        console.log('Test record saved successfully:', savedRecord);
        
        // Clean up test record
        await galaxyRepo.deleteGalaxyPermitRecord('test-workflow-id');
        console.log('Test record cleaned up');
        
        console.log('All tests passed!');
        
    } catch (error) {
        console.error('Test failed:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test
testTiDbConnection().then(() => {
    console.log('Test completed');
    process.exit(0);
}).catch((error) => {
    console.error('Test failed with error:', error);
    process.exit(1);
});
